import React, { useState } from 'react';
import {
  View,
  Pressable,
  Text,
  Animated,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';

export interface M3EButtonGroupsProps {
  /** 按钮组类型 */
  type?: 'round' | 'square';
  /** 按钮组尺寸 */
  size?: 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';
  /** 按钮数量 */
  slots?: 2 | 3 | 4 | 5;
  /** 按钮标签 */
  labels: string[];
  /** 选中的按钮索引 */
  selectedIndex?: number;
  /** 按钮点击回调 */
  onPress?: (index: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
}

export const M3EButtonGroups: React.FC<M3EButtonGroupsProps> = ({
  type = 'round',
  size = 'medium',
  slots = 3,
  labels,
  selectedIndex = 0,
  onPress,
  disabled = false,
  style,
}) => {
  const theme = useAppTheme();
  const [pressedIndex, setPressedIndex] = useState<number | null>(null);
  const [pressAnims] = useState(() =>
    Array.from({ length: slots }, () => new Animated.Value(1))
  );

  const isDark = theme.dark;

  // 基于Figma设计的尺寸规范
  const getSizeSpecs = () => {
    switch (size) {
      case 'xsmall':
        return {
          height: 32,
          paddingHorizontal: 12,
          fontSize: 11,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 16 : 8,
          letterSpacing: 0.5,
          lineHeight: 16,
          gap: 0, // 按钮间无间隙
        };
      case 'small':
        return {
          height: 40,
          paddingHorizontal: 16,
          fontSize: 14,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 20 : 10,
          letterSpacing: 0.1,
          lineHeight: 20,
          gap: 0,
        };
      case 'medium':
        return {
          height: 48,
          paddingHorizontal: 20,
          fontSize: 16,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 24 : 12,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 0,
        };
      case 'large':
        return {
          height: 56,
          paddingHorizontal: 24,
          fontSize: 16,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 28 : 14,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 0,
        };
      case 'xlarge':
        return {
          height: 64,
          paddingHorizontal: 28,
          fontSize: 18,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 32 : 16,
          letterSpacing: 0.1,
          lineHeight: 28,
          gap: 0,
        };
      default:
        return {
          height: 48,
          paddingHorizontal: 20,
          fontSize: 16,
          fontWeight: '500' as const,
          borderRadius: type === 'round' ? 24 : 12,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 0,
        };
    }
  };

  const sizeSpecs = getSizeSpecs();

  // M3E颜色规范
  const getColors = () => {
    if (isDark) {
      return {
        // 选中状态
        selectedBackground: '#D0BCFF', // Primary container
        selectedText: '#381E72', // On primary container
        selectedBorder: '#D0BCFF',
        // 未选中状态
        unselectedBackground: 'transparent',
        unselectedText: '#E6E0E9', // On surface
        unselectedBorder: '#938F99', // Outline
        // 悬停状态
        hoverOverlay: 'rgba(208, 188, 255, 0.08)',
        // 按压状态
        pressOverlay: 'rgba(208, 188, 255, 0.12)',
        // 禁用状态
        disabledBackground: 'rgba(230, 224, 233, 0.12)',
        disabledText: 'rgba(230, 224, 233, 0.38)',
        disabledBorder: 'rgba(230, 224, 233, 0.12)',
      };
    } else {
      return {
        // 选中状态
        selectedBackground: '#EADDFF', // Primary container
        selectedText: '#4F378A', // On primary container
        selectedBorder: '#EADDFF',
        // 未选中状态
        unselectedBackground: 'transparent',
        unselectedText: '#1D1B20', // On surface
        unselectedBorder: '#79747E', // Outline
        // 悬停状态
        hoverOverlay: 'rgba(79, 55, 138, 0.08)',
        // 按压状态
        pressOverlay: 'rgba(79, 55, 138, 0.12)',
        // 禁用状态
        disabledBackground: 'rgba(29, 27, 32, 0.12)',
        disabledText: 'rgba(29, 27, 32, 0.38)',
        disabledBorder: 'rgba(29, 27, 32, 0.12)',
      };
    }
  };

  const colors = getColors();

  const handlePressIn = (index: number) => {
    if (disabled) return;
    setPressedIndex(index);
    Animated.spring(pressAnims[index], {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = (index: number) => {
    if (disabled) return;
    setPressedIndex(null);
    Animated.spring(pressAnims[index], {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = (index: number) => {
    if (disabled) return;
    onPress?.(index);
  };

  const getButtonStyle = (
    index: number,
    isFirst: boolean,
    isLast: boolean
  ): ViewStyle => {
    const isSelected = selectedIndex === index;
    const isPressed = pressedIndex === index;

    let borderRadius = 0;
    if (isFirst && isLast) {
      // 单个按钮
      borderRadius = sizeSpecs.borderRadius;
    } else if (isFirst) {
      // 第一个按钮
      borderRadius =
        type === 'round' ? sizeSpecs.borderRadius : sizeSpecs.borderRadius;
    } else if (isLast) {
      // 最后一个按钮
      borderRadius =
        type === 'round' ? sizeSpecs.borderRadius : sizeSpecs.borderRadius;
    }

    return {
      height: sizeSpecs.height,
      paddingHorizontal: sizeSpecs.paddingHorizontal,
      backgroundColor: disabled
        ? colors.disabledBackground
        : isSelected
        ? colors.selectedBackground
        : colors.unselectedBackground,
      borderWidth: 1,
      borderColor: disabled
        ? colors.disabledBorder
        : isSelected
        ? colors.selectedBorder
        : colors.unselectedBorder,
      borderTopLeftRadius: isFirst ? borderRadius : 0,
      borderBottomLeftRadius: isFirst ? borderRadius : 0,
      borderTopRightRadius: isLast ? borderRadius : 0,
      borderBottomRightRadius: isLast ? borderRadius : 0,
      borderLeftWidth: isFirst ? 1 : 0,
      borderRightWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
      position: 'relative',
      overflow: 'hidden',
    };
  };

  const getTextStyle = (index: number): TextStyle => {
    const isSelected = selectedIndex === index;

    return {
      fontSize: sizeSpecs.fontSize,
      fontWeight: sizeSpecs.fontWeight,
      letterSpacing: sizeSpecs.letterSpacing,
      lineHeight: sizeSpecs.lineHeight,
      color: disabled
        ? colors.disabledText
        : isSelected
        ? colors.selectedText
        : colors.unselectedText,
      textAlign: 'center',
    };
  };

  const renderButton = (label: string, index: number) => {
    const isFirst = index === 0;
    const isLast = index === labels.length - 1;
    const isPressed = pressedIndex === index;

    return (
      <Animated.View
        key={index}
        style={[{ transform: [{ scale: pressAnims[index] }] }, { flex: 1 }]}
      >
        <Pressable style={getButtonStyle(index, isFirst, isLast)}
          onPressIn={() => handlePressIn(index)}
          onPressOut={() => handlePressOut(index)}
          onPress={() => handlePress(index)}
          disabled={disabled}
        >
          {/* 状态层 */}
          {isPressed && (
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: colors.pressOverlay,
              }}
            />
          )}

          <Text style={getTextStyle(index)}>{label}</Text>
        </Pressable>
      </Animated.View>
    );
  };

  return (
    <View style={[{ flexDirection: 'row' }, style]}>
      {labels.slice(0, slots).map((label, index) => renderButton(label, index))}
    </View>
  );
};

export default M3EButtonGroups;
