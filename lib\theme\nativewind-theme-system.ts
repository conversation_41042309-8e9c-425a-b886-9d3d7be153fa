/**
 * NativeWind 主题系统 - 最佳实践实现
 * 
 * 基于 NativeWind v4 和 Tailwind CSS 的主题系统
 * 支持深色模式、动态主题切换和 Material Design 3 色彩系统
 */

import { useColorScheme } from 'nativewind';
import { useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'system';

// 主题状态接口
interface ThemeState {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
}

// 主题存储
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      mode: 'system',
      setMode: (mode) => set({ mode }),
      toggleMode: () => {
        const current = get().mode;
        const next = current === 'light' ? 'dark' : 'light';
        set({ mode: next });
      },
    }),
    {
      name: 'nativewind-theme-storage',
    }
  )
);

// NativeWind 主题 Hook
export function useNativeWindTheme() {
  const { colorScheme, setColorScheme } = useColorScheme();
  const { mode, setMode, toggleMode } = useThemeStore();

  // 同步主题模式
  useEffect(() => {
    if (mode === 'system') {
      // 跟随系统主题
      setColorScheme('system');
    } else {
      // 使用指定主题
      setColorScheme(mode);
    }
  }, [mode, setColorScheme]);

  return {
    // 当前主题模式
    mode,
    // 实际的颜色方案
    colorScheme,
    // 是否为深色模式
    isDark: colorScheme === 'dark',
    // 设置主题模式
    setMode,
    // 切换主题
    toggleMode,
    // 设置颜色方案（直接控制 NativeWind）
    setColorScheme,
  };
}

// Material Design 3 颜色令牌
export const M3_COLOR_TOKENS = {
  // Primary 色彩
  primary: {
    50: '#f0f4ff',
    100: '#e0e7ff', 
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1', // 主色
    600: '#4f46e5',
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
    950: '#1e1b4b',
  },
  // Surface 色彩
  surface: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },
  // Error 色彩
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // 错误色
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
  // Success 色彩
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // 成功色
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  // Warning 色彩
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // 警告色
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
} as const;

// 语义化颜色映射
export const SEMANTIC_COLORS = {
  light: {
    // 背景色
    background: 'bg-white',
    backgroundSecondary: 'bg-surface-50',
    backgroundTertiary: 'bg-surface-100',
    
    // 表面色
    surface: 'bg-white',
    surfaceContainer: 'bg-surface-100',
    surfaceContainerHigh: 'bg-surface-200',
    
    // 文本色
    text: 'text-surface-900',
    textSecondary: 'text-surface-600',
    textTertiary: 'text-surface-400',
    
    // 边框色
    border: 'border-surface-200',
    borderSecondary: 'border-surface-300',
    
    // 主色
    primary: 'bg-primary-500',
    primaryText: 'text-primary-500',
    onPrimary: 'text-white',
    
    // 错误色
    error: 'bg-error-500',
    errorText: 'text-error-500',
    onError: 'text-white',
    
    // 成功色
    success: 'bg-success-500',
    successText: 'text-success-500',
    onSuccess: 'text-white',
    
    // 警告色
    warning: 'bg-warning-500',
    warningText: 'text-warning-500',
    onWarning: 'text-white',
  },
  dark: {
    // 背景色
    background: 'dark:bg-surface-950',
    backgroundSecondary: 'dark:bg-surface-900',
    backgroundTertiary: 'dark:bg-surface-800',
    
    // 表面色
    surface: 'dark:bg-surface-900',
    surfaceContainer: 'dark:bg-surface-800',
    surfaceContainerHigh: 'dark:bg-surface-700',
    
    // 文本色
    text: 'dark:text-surface-50',
    textSecondary: 'dark:text-surface-300',
    textTertiary: 'dark:text-surface-500',
    
    // 边框色
    border: 'dark:border-surface-700',
    borderSecondary: 'dark:border-surface-600',
    
    // 主色
    primary: 'dark:bg-primary-400',
    primaryText: 'dark:text-primary-400',
    onPrimary: 'dark:text-surface-900',
    
    // 错误色
    error: 'dark:bg-error-400',
    errorText: 'dark:text-error-400',
    onError: 'dark:text-surface-900',
    
    // 成功色
    success: 'dark:bg-success-400',
    successText: 'dark:text-success-400',
    onSuccess: 'dark:text-surface-900',
    
    // 警告色
    warning: 'dark:bg-warning-400',
    warningText: 'dark:text-warning-400',
    onWarning: 'dark:text-surface-900',
  },
} as const;

// 组合语义化类名
export function getSemanticClasses(type: keyof typeof SEMANTIC_COLORS.light) {
  return `${SEMANTIC_COLORS.light[type]} ${SEMANTIC_COLORS.dark[type]}`;
}

// 主题工具函数
export const themeUtils = {
  // 获取背景类名
  getBackground: (variant: 'primary' | 'secondary' | 'tertiary' = 'primary') => {
    switch (variant) {
      case 'secondary':
        return getSemanticClasses('backgroundSecondary');
      case 'tertiary':
        return getSemanticClasses('backgroundTertiary');
      default:
        return getSemanticClasses('background');
    }
  },
  
  // 获取文本类名
  getText: (variant: 'primary' | 'secondary' | 'tertiary' = 'primary') => {
    switch (variant) {
      case 'secondary':
        return getSemanticClasses('textSecondary');
      case 'tertiary':
        return getSemanticClasses('textTertiary');
      default:
        return getSemanticClasses('text');
    }
  },
  
  // 获取表面类名
  getSurface: (variant: 'default' | 'container' | 'high' = 'default') => {
    switch (variant) {
      case 'container':
        return getSemanticClasses('surfaceContainer');
      case 'high':
        return getSemanticClasses('surfaceContainerHigh');
      default:
        return getSemanticClasses('surface');
    }
  },
  
  // 获取边框类名
  getBorder: (variant: 'primary' | 'secondary' = 'primary') => {
    switch (variant) {
      case 'secondary':
        return getSemanticClasses('borderSecondary');
      default:
        return getSemanticClasses('border');
    }
  },
  
  // 获取状态颜色类名
  getStatusColor: (status: 'primary' | 'error' | 'success' | 'warning', variant: 'bg' | 'text' = 'bg') => {
    const key = variant === 'bg' ? status : `${status}Text` as keyof typeof SEMANTIC_COLORS.light;
    return getSemanticClasses(key);
  },
};

// 导出默认主题配置
export const defaultThemeConfig = {
  colorTokens: M3_COLOR_TOKENS,
  semanticColors: SEMANTIC_COLORS,
  utils: themeUtils,
};
