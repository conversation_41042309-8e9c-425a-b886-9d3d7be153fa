import React from 'react';
import { Dimensions, View, Image } from 'react-native';
import { Pressable } from '@/components/base';

import { Text } from '@/components/base';

import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { M3EDivider } from '@/components/ui/m3e-divider';
import { User as UserType } from '@/types/user';
import { BookOpen, Crown, UserPlus, Check } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

// Calculate width for 2 columns with standard medium spacing
const { width } = Dimensions.get('window');
const approxSpacingMd = 16;
const CARD_WIDTH = (width - approxSpacingMd * 3) / 2;

interface UserCardProps {
  user: UserType;
  onPress?: (userId: string) => void;
  onFollow?: (userId: string) => void;
  isFollowing?: boolean;
  isFollowingInProgress?: boolean;
}

export default function UserCard({
  user,
  onPress,
  onFollow,
  isFollowing = false,
  isFollowingInProgress = false,
}: UserCardProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Pressable
      className={`p-4 rounded-lg border mb-4 ${
        isDark
          ? 'border-outline-700 bg-background-800'
          : 'border-outline-200 bg-background-50'
      }`}
      style={{ width: CARD_WIDTH }}
      onPress={() => onPress?.(user.id)}
      disabled={!onPress}
    >
      <View className="flex flex-row items-center mb-2">
        <View className="relative">
          <Image
            source={{ uri: user.avatar }}
            className="w-10 h-10 rounded-full"
            alt={user.displayName}
          />

          {user.isPremium && (
            <View className="absolute -top-1 left-7 bg-yellow-400 p-[3px] rounded-lg z-10">
              <Crown size={10} color="#000" />
            </View>
          )}
        </View>

        <Pressable
          className={`ml-auto w-8 h-8 rounded-full items-center justify-center ${
            isFollowing
              ? 'bg-transparent border border-primary-500'
              : isDark
              ? 'bg-primary-600'
              : 'bg-primary-500'
          }`}
          onPress={() => onFollow?.(user.id)}
          disabled={!onFollow || isFollowingInProgress}
        >
          {isFollowingInProgress ? (
            <M3EProgressIndicator
              size="small"
              color={isDark ? 'white' : 'white'}
            />
          ) : isFollowing ? (
            <Check size={16} className="text-primary-500" />
          ) : (
            <UserPlus size={16} className="text-white" />
          )}
        </Pressable>
      </View>

      <Text
        className={`font-bold text-sm ${
          isDark ? 'text-typography-50' : 'text-typography-900'
        }`}
        numberOfLines={1}
      >
        {user.displayName}
      </Text>

      <Text
        className={`text-xs ${
          isDark ? 'text-typography-400' : 'text-typography-500'
        }`}
        numberOfLines={1}
      >
        @{user.username}
      </Text>

      <View
        className="flex flex-row"
        className={`items-center pt-2 mt-1 ${
          isDark ? 'border-t border-outline-700' : 'border-t border-outline-200'
        }`}
      >
        <View className="flex flex-row items-center space-x-1">
          <BookOpen
            size={12}
            className={isDark ? 'text-typography-400' : 'text-typography-500'}
          />
          <Text
            className={`text-xs ${
              isDark ? 'text-typography-400' : 'text-typography-500'
            }`}
          >
            {user.storiesCount}
          </Text>
        </View>

        <M3EDivider orientation="vertical" className="h-3 mx-2" />

        <Text
          className={`text-xs ${
            isDark ? 'text-typography-400' : 'text-typography-500'
          }`}
        >
          {user.followers} {t('followersSuffix', '粉丝')}
        </Text>
      </View>
    </Pressable>
  );
}
