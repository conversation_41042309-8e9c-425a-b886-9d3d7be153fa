/**
 * 迁移到M3E组件脚本
 * 
 * 此脚本用于自动将基础组件迁移到M3E组件
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 迁移规则配置
const migrationRules = [
  // 高优先级组件
  {
    name: 'Text',
    oldImport: /import\s+{([^}]*\bText\b[^}]*)}\s+from\s+['"]react-native['"];?/g,
    newImport: "import { M3EText } from '@/components/ui/m3e-typography';",
    replaceUsage: [
      { from: /<Text\b/g, to: '<M3EText' },
      { from: /<\/Text>/g, to: '</M3EText>' },
      { from: /\bText\s*\./g, to: 'M3EText.' }
    ],
    priority: 1
  },
  {
    name: 'Button',
    oldImport: /import\s+{([^}]*\bButton\b[^}]*)}\s+from\s+['"]@\/components\/base['"];?/g,
    newImport: "import { M3EButton } from '@/components/ui/m3e-button';",
    replaceUsage: [
      { from: /<Button\b/g, to: '<M3EButton' },
      { from: /<\/Button>/g, to: '</M3EButton>' },
      { from: /\bButton\s*\./g, to: 'M3EButton.' }
    ],
    priority: 1
  },
  {
    name: 'TextInput',
    oldImport: /import\s+{([^}]*\bTextInput\b[^}]*)}\s+from\s+['"]react-native['"];?/g,
    newImport: "import { M3ETextField } from '@/components/ui/m3e-text-field';",
    replaceUsage: [
      { from: /<TextInput\b/g, to: '<M3ETextField' },
      { from: /<\/TextInput>/g, to: '</M3ETextField>' },
      { from: /\bTextInput\s*\./g, to: 'M3ETextField.' }
    ],
    priority: 1
  },
  {
    name: 'Modal',
    oldImport: /import\s+{([^}]*\bModal\b[^}]*)}\s+from\s+['"]react-native['"];?/g,
    newImport: "import { M3EDialog } from '@/components/ui/m3e-dialog';",
    replaceUsage: [
      { from: /<Modal\b/g, to: '<M3EDialog' },
      { from: /<\/Modal>/g, to: '</M3EDialog>' },
      { from: /\bModal\s*\./g, to: 'M3EDialog.' }
    ],
    priority: 2
  },
  {
    name: 'ActivityIndicator',
    oldImport: /import\s+{([^}]*\bActivityIndicator\b[^}]*)}\s+from\s+['"]react-native['"];?/g,
    newImport: "import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';",
    replaceUsage: [
      { from: /<ActivityIndicator\b/g, to: '<M3EProgressIndicator' },
      { from: /<\/ActivityIndicator>/g, to: '</M3EProgressIndicator>' },
      { from: /\bActivityIndicator\s*\./g, to: 'M3EProgressIndicator.' }
    ],
    priority: 2
  }
];

// 迁移文件
const migrateFile = (filePath, rules) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedMigrations = [];

    for (const rule of rules) {
      let ruleApplied = false;
      
      // 检查是否已经使用了M3E组件
      const isAlreadyM3E = content.includes('@/components/ui/m3e-') || 
                          content.includes('M3E');
      
      // 如果文件已经大量使用M3E组件，跳过基础组件的迁移
      if (isAlreadyM3E && rule.priority === 1) {
        continue;
      }
      
      // 替换导入
      if (rule.oldImport.test(content)) {
        // 检查是否已经有新的导入
        if (!content.includes(rule.newImport.replace(/import\s+{[^}]*}\s+from\s+/, '').replace(/['"];?/, ''))) {
          // 在文件顶部添加新的导入
          const importSection = content.match(/^((?:import[^;]+;?\s*\n)*)/);
          if (importSection) {
            content = content.replace(importSection[0], importSection[0] + rule.newImport + '\n');
          } else {
            content = rule.newImport + '\n' + content;
          }
          ruleApplied = true;
        }
        
        // 移除旧的导入中的特定组件
        content = content.replace(rule.oldImport, (match, imports) => {
          const importList = imports.split(',').map(imp => imp.trim()).filter(imp => !imp.includes(rule.name));
          if (importList.length === 0) {
            return ''; // 完全移除这行导入
          }
          return `import { ${importList.join(', ')} } from 'react-native';`;
        });
        ruleApplied = true;
      }
      
      // 替换使用
      for (const usage of rule.replaceUsage) {
        if (usage.from.test(content)) {
          content = content.replace(usage.from, usage.to);
          ruleApplied = true;
        }
      }
      
      if (ruleApplied) {
        appliedMigrations.push(rule.name);
        hasChanges = true;
      }
    }

    // 清理空的导入行
    content = content.replace(/import\s+{[\s,]*}\s+from\s+['"][^'"]*['"];?\s*\n/g, '');
    
    // 清理多余的空行
    content = content.replace(/\n\n\n+/g, '\n\n');

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { file: filePath, migrations: appliedMigrations };
    }

    return null;
  } catch (error) {
    console.error(`迁移文件 ${filePath} 时出错:`, error.message);
    return null;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移到M3E组件...');
  console.log('='.repeat(50));
  
  // 按优先级排序规则
  const sortedRules = migrationRules.sort((a, b) => a.priority - b.priority);
  
  console.log('📋 迁移规则:');
  sortedRules.forEach(rule => {
    console.log(`   ${rule.priority}. ${rule.name} → M3E${rule.name}`);
  });
  
  console.log('\n🔍 扫描文件...');
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  const migratedFiles = [];
  
  for (const file of allFiles) {
    const result = migrateFile(file, sortedRules);
    if (result) {
      migratedFiles.push(result);
    }
  }
  
  console.log('\n📊 迁移结果:');
  console.log('='.repeat(50));
  
  if (migratedFiles.length === 0) {
    console.log('✅ 没有发现需要迁移的组件!');
    console.log('🎉 所有组件可能已经使用了M3E组件。');
  } else {
    console.log(`🔄 迁移了 ${migratedFiles.length} 个文件:`);
    
    // 按迁移的组件类型统计
    const migrationStats = {};
    
    migratedFiles.forEach(({ file, migrations }) => {
      migrations.forEach(migration => {
        if (!migrationStats[migration]) {
          migrationStats[migration] = [];
        }
        migrationStats[migration].push(file);
      });
    });
    
    Object.entries(migrationStats).forEach(([component, files]) => {
      console.log(`\n📦 ${component} (${files.length} 个文件):`);
      files.slice(0, 5).forEach(file => {
        const relativePath = path.relative(process.cwd(), file);
        console.log(`   - ${relativePath}`);
      });
      if (files.length > 5) {
        console.log(`   ... 还有 ${files.length - 5} 个文件`);
      }
    });
  }
  
  console.log('\n📝 下一步行动:');
  console.log('1. 运行应用程序测试迁移后的功能');
  console.log('2. 检查是否有编译错误');
  console.log('3. 删除不再使用的旧组件文件');
  console.log('4. 更新组件文档');
  
  console.log('\n🎉 M3E组件迁移完成!');
  console.log('='.repeat(50));
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findAllFiles, migrateFile, migrationRules };
