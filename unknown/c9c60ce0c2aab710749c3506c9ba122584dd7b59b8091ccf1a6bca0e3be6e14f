/**
 * NativeWind 主题提供者
 * 
 * 基于 NativeWind 最佳实践的主题提供者组件
 * 提供统一的主题管理和颜色系统
 */

import React, { createContext, useContext, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { useColorScheme } from 'nativewind';
import { useNativeWindTheme, themeUtils, M3_COLOR_TOKENS } from './nativewind-theme-system';

// 主题上下文类型
interface ThemeContextType {
  // 主题状态
  mode: 'light' | 'dark' | 'system';
  colorScheme: 'light' | 'dark';
  isDark: boolean;
  
  // 主题控制
  setMode: (mode: 'light' | 'dark' | 'system') => void;
  toggleMode: () => void;
  
  // 颜色系统
  colors: {
    // 背景色
    background: string;
    backgroundSecondary: string;
    backgroundTertiary: string;
    
    // 表面色
    surface: string;
    surfaceContainer: string;
    surfaceContainerHigh: string;
    
    // 文本色
    onSurface: string;
    onSurfaceVariant: string;
    onBackground: string;
    
    // 主色
    primary: string;
    onPrimary: string;
    primaryContainer: string;
    onPrimaryContainer: string;
    
    // 边框色
    outline: string;
    outlineVariant: string;
    
    // 状态色
    error: string;
    onError: string;
    success: string;
    warning: string;
  };
  
  // 工具函数
  utils: typeof themeUtils;
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | null>(null);

// 主题提供者组件
export function NativeWindThemeProvider({ children }: { children: React.ReactNode }) {
  const { mode, colorScheme, isDark, setMode, toggleMode } = useNativeWindTheme();

  // 根据当前主题获取颜色值
  const colors = React.useMemo(() => {
    if (isDark) {
      return {
        // 背景色
        background: '#141218', // surface-dark
        backgroundSecondary: '#1D1B20', // surface-container-dark
        backgroundTertiary: '#2B2930', // surface-container-high-dark
        
        // 表面色
        surface: '#141218',
        surfaceContainer: '#1D1B20',
        surfaceContainerHigh: '#2B2930',
        
        // 文本色
        onSurface: '#E6E0E9', // on-surface-dark
        onSurfaceVariant: '#CAC4D0', // on-surface-variant-dark
        onBackground: '#E6E0E9',
        
        // 主色
        primary: M3_COLOR_TOKENS.primary[400], // 深色模式使用较亮的主色
        onPrimary: '#1E1B4B',
        primaryContainer: '#312E81',
        onPrimaryContainer: '#E0E7FF',
        
        // 边框色
        outline: '#938F99',
        outlineVariant: '#49454F', // outline-variant-dark
        
        // 状态色
        error: M3_COLOR_TOKENS.error[400],
        onError: '#450A0A',
        success: M3_COLOR_TOKENS.success[400],
        warning: M3_COLOR_TOKENS.warning[400],
      };
    } else {
      return {
        // 背景色
        background: '#FEF7FF', // surface
        backgroundSecondary: '#F3EDF7', // surface-container
        backgroundTertiary: '#F3EDF7', // surface-container-high
        
        // 表面色
        surface: '#FEF7FF',
        surfaceContainer: '#F3EDF7',
        surfaceContainerHigh: '#F3EDF7',
        
        // 文本色
        onSurface: '#1D1B20', // on-surface
        onSurfaceVariant: '#49454F', // on-surface-variant
        onBackground: '#1D1B20',
        
        // 主色
        primary: M3_COLOR_TOKENS.primary[500], // 亮色模式使用标准主色
        onPrimary: '#FFFFFF',
        primaryContainer: '#E0E7FF',
        onPrimaryContainer: '#1E1B4B',
        
        // 边框色
        outline: '#79747E',
        outlineVariant: '#CAC4D0', // outline-variant
        
        // 状态色
        error: M3_COLOR_TOKENS.error[500],
        onError: '#FFFFFF',
        success: M3_COLOR_TOKENS.success[500],
        warning: M3_COLOR_TOKENS.warning[500],
      };
    }
  }, [isDark]);

  // 主题上下文值
  const contextValue: ThemeContextType = {
    mode,
    colorScheme,
    isDark,
    setMode,
    toggleMode,
    colors,
    utils: themeUtils,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {/* 状态栏主题 */}
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {children}
    </ThemeContext.Provider>
  );
}

// 主题 Hook
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a NativeWindThemeProvider');
  }
  return context;
}

// 兼容性 Hook（保持与现有代码的兼容性）
export function useUnifiedTheme() {
  const theme = useTheme();
  return {
    colors: theme.colors,
    isDark: theme.isDark,
    mode: theme.colorScheme,
    isSystemTheme: theme.mode === 'system',
    setTheme: (newTheme: 'light' | 'dark') => theme.setMode(newTheme),
    setIsSystemTheme: (isSystem: boolean) => {
      if (isSystem) {
        theme.setMode('system');
      }
    },
  };
}

// 导出类型
export type { ThemeContextType };

// 默认导出
export default NativeWindThemeProvider;
