/**
 * 分析重复组件脚本
 * 
 * 此脚本用于：
 * 1. 检查项目中的重复组件
 * 2. 分析组件使用情况
 * 3. 生成清理建议
 */

const fs = require('fs');
const path = require('path');

// 潜在重复的组件映射
const duplicateComponentMappings = [
  {
    old: 'components/ui/badge',
    new: 'components/ui/m3e-badge',
    component: 'Badge',
    m3eComponent: 'M3EBadge',
    reason: 'M3E Badge 提供更好的 Material Design 3 支持'
  },
  {
    old: 'components/ui/card',
    new: 'components/ui/m3e-card',
    component: 'Card',
    m3eComponent: 'M3ECard',
    reason: 'M3E Card 提供完整的 Material Design 3 卡片规范'
  },
  {
    old: 'components/ui/divider',
    new: 'components/ui/m3e-divider',
    component: 'Divider',
    m3eComponent: 'M3EDivider',
    reason: 'M3E Divider 已集成统一主题系统'
  },
  {
    old: 'components/ui/input',
    new: 'components/ui/m3e-text-field',
    component: 'Input',
    m3eComponent: 'M3ETextField',
    reason: 'M3E TextField 提供更好的输入体验'
  },
  {
    old: 'components/ui/modal',
    new: 'components/ui/m3e-dialog',
    component: 'Modal',
    m3eComponent: 'M3EDialog',
    reason: 'M3E Dialog 提供更好的模态框体验'
  },
  {
    old: 'components/ui/radio',
    new: 'components/ui/m3e-radio-button',
    component: 'Radio',
    m3eComponent: 'M3ERadioButton',
    reason: 'M3E RadioButton 符合 Material Design 3 规范'
  },
  {
    old: 'components/ui/switch',
    new: 'components/ui/m3e-switch',
    component: 'Switch',
    m3eComponent: 'M3ESwitch',
    reason: 'M3E Switch 提供更好的开关体验'
  },
  {
    old: 'components/ui/tabs',
    new: 'components/ui/m3e-tabs',
    component: 'Tabs',
    m3eComponent: 'M3ETabs',
    reason: 'M3E Tabs 提供更好的标签页体验'
  },
  {
    old: 'components/ui/search-bar',
    new: 'components/ui/m3e-search',
    component: 'SearchBar',
    m3eComponent: 'M3ESearch',
    reason: 'M3E Search 提供更好的搜索体验'
  },
  {
    old: 'components/ui/spinner',
    new: 'components/ui/m3e-loading-indicator',
    component: 'Spinner',
    m3eComponent: 'M3ELoadingIndicator',
    reason: 'M3E LoadingIndicator 提供更好的加载指示器'
  }
];

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 检查组件是否存在
const checkComponentExists = (componentPath) => {
  try {
    return fs.existsSync(componentPath);
  } catch {
    return false;
  }
};

// 分析文件中的组件使用情况
const analyzeComponentUsage = (filePath, componentName, importPath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查导入
    const importRegex = new RegExp(`from\\s+['"]${importPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
    const hasImport = importRegex.test(content);
    
    // 检查使用
    const usageRegex = new RegExp(`<${componentName}\\b`, 'g');
    const usageMatches = content.match(usageRegex);
    const usageCount = usageMatches ? usageMatches.length : 0;
    
    return {
      hasImport,
      usageCount,
      file: filePath
    };
  } catch (error) {
    return {
      hasImport: false,
      usageCount: 0,
      file: filePath,
      error: error.message
    };
  }
};

// 分析重复组件
const analyzeDuplicateComponents = () => {
  console.log('🔍 开始分析重复组件...');
  console.log('='.repeat(50));
  
  const allFiles = findAllFiles(process.cwd());
  const results = [];
  
  for (const mapping of duplicateComponentMappings) {
    console.log(`\n📦 分析组件: ${mapping.component} vs ${mapping.m3eComponent}`);
    
    const oldExists = checkComponentExists(mapping.old);
    const newExists = checkComponentExists(mapping.new);
    
    console.log(`  旧组件存在: ${oldExists ? '✅' : '❌'} (${mapping.old})`);
    console.log(`  M3E组件存在: ${newExists ? '✅' : '❌'} (${mapping.new})`);
    
    if (!oldExists && !newExists) {
      console.log(`  ⚠️  两个组件都不存在，跳过分析`);
      continue;
    }
    
    if (!oldExists) {
      console.log(`  ✅ 旧组件已不存在，无需清理`);
      continue;
    }
    
    if (!newExists) {
      console.log(`  ⚠️  M3E组件不存在，建议创建`);
      results.push({
        ...mapping,
        status: 'missing_m3e',
        oldExists,
        newExists,
        oldUsage: [],
        newUsage: []
      });
      continue;
    }
    
    // 分析使用情况
    console.log(`  🔍 分析使用情况...`);
    
    const oldUsage = [];
    const newUsage = [];
    
    for (const file of allFiles) {
      const oldAnalysis = analyzeComponentUsage(file, mapping.component, mapping.old);
      const newAnalysis = analyzeComponentUsage(file, mapping.m3eComponent, mapping.new);
      
      if (oldAnalysis.hasImport || oldAnalysis.usageCount > 0) {
        oldUsage.push(oldAnalysis);
      }
      
      if (newAnalysis.hasImport || newAnalysis.usageCount > 0) {
        newUsage.push(newAnalysis);
      }
    }
    
    console.log(`    旧组件使用: ${oldUsage.length} 个文件`);
    console.log(`    M3E组件使用: ${newUsage.length} 个文件`);
    
    const status = oldUsage.length === 0 ? 'can_remove' : 'needs_migration';
    
    results.push({
      ...mapping,
      status,
      oldExists,
      newExists,
      oldUsage,
      newUsage
    });
  }
  
  return results;
};

// 生成清理报告
const generateCleanupReport = (results) => {
  console.log('\n📊 重复组件分析报告');
  console.log('='.repeat(50));
  
  const canRemove = results.filter(r => r.status === 'can_remove');
  const needsMigration = results.filter(r => r.status === 'needs_migration');
  const missingM3E = results.filter(r => r.status === 'missing_m3e');
  
  console.log(`\n✅ 可以直接删除的组件 (${canRemove.length} 个):`);
  canRemove.forEach(item => {
    console.log(`  - ${item.old} (${item.component})`);
    console.log(`    原因: ${item.reason}`);
  });
  
  console.log(`\n🔄 需要迁移的组件 (${needsMigration.length} 个):`);
  needsMigration.forEach(item => {
    console.log(`  - ${item.old} -> ${item.new}`);
    console.log(`    使用文件数: ${item.oldUsage.length}`);
    console.log(`    原因: ${item.reason}`);
    
    // 显示前5个使用文件
    const topFiles = item.oldUsage.slice(0, 5);
    topFiles.forEach(usage => {
      const relativePath = path.relative(process.cwd(), usage.file);
      console.log(`      ${relativePath} (${usage.usageCount} 次使用)`);
    });
    
    if (item.oldUsage.length > 5) {
      console.log(`      ... 还有 ${item.oldUsage.length - 5} 个文件`);
    }
  });
  
  console.log(`\n⚠️  缺少M3E组件 (${missingM3E.length} 个):`);
  missingM3E.forEach(item => {
    console.log(`  - 需要创建: ${item.new} (${item.m3eComponent})`);
    console.log(`    原因: ${item.reason}`);
  });
  
  return {
    canRemove,
    needsMigration,
    missingM3E,
    total: results.length
  };
};

// 主函数
const main = () => {
  try {
    const results = analyzeDuplicateComponents();
    const report = generateCleanupReport(results);
    
    console.log('\n📝 下一步建议:');
    console.log('='.repeat(50));
    
    if (report.canRemove.length > 0) {
      console.log('1. 删除未使用的旧组件:');
      report.canRemove.forEach(item => {
        console.log(`   rm -rf ${item.old}`);
      });
    }
    
    if (report.needsMigration.length > 0) {
      console.log('2. 迁移使用旧组件的文件:');
      console.log('   - 运行组件迁移脚本');
      console.log('   - 手动更新复杂的使用场景');
    }
    
    if (report.missingM3E.length > 0) {
      console.log('3. 创建缺少的M3E组件:');
      report.missingM3E.forEach(item => {
        console.log(`   - 创建 ${item.new}`);
      });
    }
    
    console.log('\n🎯 完成后项目将:');
    console.log('   • 消除重复组件');
    console.log('   • 统一使用M3E设计系统');
    console.log('   • 提高代码维护性');
    
  } catch (error) {
    console.error('❌ 分析过程中出错:', error);
  }
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  analyzeDuplicateComponents,
  generateCleanupReport,
  duplicateComponentMappings
};
