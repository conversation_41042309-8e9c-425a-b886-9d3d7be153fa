/**
 * 全面修复导入问题脚本
 *
 * 此脚本用于一次性修复所有导入相关的问题
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];

  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          // 跳过特定目录
          if (
            ![
              'node_modules',
              '.git',
              '.mine',
              '.cursor',
              'dist',
              'build',
              'scripts',
            ].includes(item)
          ) {
            scanDir(fullPath);
          }
        } else if (
          stat.isFile() &&
          (item.endsWith('.tsx') || item.endsWith('.ts'))
        ) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };

  scanDir(dir);
  return files;
};

// 修复文件中的问题
const fixFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const issues = [];

    // 1. 修复重复的 Text 导入
    const textImportRegex =
      /import\s+{[^}]*Text[^}]*}\s+from\s+['"]@\/components\/base['"];?\s*\n.*import\s+{[^}]*Text[^}]*}\s+from\s+['"]react-native['"];?/g;
    if (textImportRegex.test(content)) {
      content = content.replace(
        /import\s+{[^}]*Text[^}]*}\s+from\s+['"]@\/components\/base['"];?\s*\n/g,
        ''
      );
      hasChanges = true;
      issues.push('修复重复的 Text 导入');
    }

    // 2. 修复重复的 Button 导入
    const buttonImportRegex =
      /import\s+{[^}]*Button[^}]*}\s+from\s+['"]@\/components\/base['"];?\s*\n.*import\s+{[^}]*Button[^}]*}\s+from\s+['"]@\/components\/ui\/m3e-button['"];?/g;
    if (buttonImportRegex.test(content)) {
      content = content.replace(
        /import\s+{[^}]*Button[^}]*}\s+from\s+['"]@\/components\/base['"];?\s*\n/g,
        ''
      );
      hasChanges = true;
      issues.push('修复重复的 Button 导入');
    }

    // 3. 修复重复的布局组件导入
    const layoutImportRegex =
      /import\s+{[^}]*(Box|VStack|HStack|Center)[^}]*}\s+from\s+['"]@\/components\/base['"];?/g;
    if (layoutImportRegex.test(content)) {
      content = content.replace(layoutImportRegex, '');
      hasChanges = true;
      issues.push('修复重复的布局组件导入');
    }

    // 4. 修复 m3e-text 导入路径
    const m3eTextImportRegex = /from\s+['"]@\/components\/ui\/m3e-text['"];?/g;
    if (m3eTextImportRegex.test(content)) {
      content = content.replace(
        m3eTextImportRegex,
        'from "@/components/ui/m3e-typography";'
      );
      hasChanges = true;
      issues.push('修复 m3e-text 导入路径');
    }

    // 5. 修复不存在的 pressable 导入
    const pressableImportRegex =
      /import\s+{[^}]*Pressable[^}]*}\s+from\s+['"]@\/components\/ui\/pressable\/index['"];?/g;
    if (pressableImportRegex.test(content)) {
      // 检查是否已经有 react-native 的导入
      if (content.includes("from 'react-native'")) {
        // 如果已有 react-native 导入，添加 Pressable 到现有导入
        content = content.replace(
          /import\s+{([^}]*)}\s+from\s+['"]react-native['"];?/,
          (match, imports) => {
            if (!imports.includes('Pressable')) {
              return `import { ${imports.trim()}, Pressable } from 'react-native';`;
            }
            return match;
          }
        );
      } else {
        // 如果没有 react-native 导入，添加新的导入
        content = content.replace(
          pressableImportRegex,
          "import { Pressable } from 'react-native';"
        );
      }
      // 移除原来的错误导入
      content = content.replace(pressableImportRegex, '');
      hasChanges = true;
      issues.push('修复 Pressable 导入路径');
    }

    // 6. 修复不存在的 image 组件导入
    const imageImportRegex =
      /import\s+{[^}]*Image[^}]*}\s+from\s+['"]@?[^'"]*\/ui\/image['"];?/g;
    if (imageImportRegex.test(content)) {
      content = content.replace(imageImportRegex, '');
      // 检查是否已经有 react-native 的导入
      if (content.includes("from 'react-native'")) {
        // 如果已有 react-native 导入，添加 Image 到现有导入
        content = content.replace(
          /import\s+{([^}]*)}\s+from\s+['"]react-native['"];?/,
          (match, imports) => {
            if (!imports.includes('Image')) {
              return `import { ${imports.trim()}, Image } from 'react-native';`;
            }
            return match;
          }
        );
      } else {
        // 如果没有 react-native 导入，添加新的导入
        const importSection = content.match(/^((?:import[^;]+;?\s*\n)*)/);
        if (importSection) {
          content = content.replace(
            importSection[0],
            importSection[0] + "import { Image } from 'react-native';\n"
          );
        }
      }
      hasChanges = true;
      issues.push('修复 Image 导入路径');
    }

    // 7. 清理空的导入行
    content = content.replace(
      /import\s+{[\s,]*}\s+from\s+['"][^'"]*['"];?\s*\n/g,
      ''
    );

    // 8. 清理多余的空行
    content = content.replace(/\n\n\n+/g, '\n\n');

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { file: filePath, issues };
    }

    return null;
  } catch (error) {
    console.error(`修复文件 ${filePath} 时出错:`, error.message);
    return null;
  }
};

// 主函数
const main = () => {
  console.log('🔧 开始全面修复导入问题...');
  console.log('='.repeat(50));

  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);

  const fixedFiles = [];

  for (const file of allFiles) {
    const result = fixFile(file);
    if (result) {
      fixedFiles.push(result);
    }
  }

  console.log('\n📊 修复结果:');
  console.log('='.repeat(50));

  if (fixedFiles.length === 0) {
    console.log('✅ 没有发现需要修复的导入问题!');
  } else {
    console.log(`🔧 修复了 ${fixedFiles.length} 个文件:`);
    fixedFiles.forEach(({ file, issues }) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach((issue) => console.log(`   - ${issue}`));
    });
  }

  console.log('\n🎉 导入问题修复完成!');
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findAllFiles, fixFile };
