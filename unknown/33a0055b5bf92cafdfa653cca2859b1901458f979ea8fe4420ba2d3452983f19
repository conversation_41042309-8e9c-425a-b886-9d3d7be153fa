/**
 * 分析组件迁移脚本
 * 
 * 此脚本用于分析哪些组件可以迁移到M3E组件
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 获取可用的M3E组件
const getAvailableM3EComponents = () => {
  const m3eDir = path.join(process.cwd(), 'components', 'ui');
  const m3eComponents = [];
  
  try {
    const items = fs.readdirSync(m3eDir);
    for (const item of items) {
      if (item.startsWith('m3e-') && fs.statSync(path.join(m3eDir, item)).isDirectory()) {
        m3eComponents.push(item);
      }
    }
  } catch (error) {
    console.error('读取M3E组件目录时出错:', error.message);
  }
  
  return m3eComponents;
};

// 分析文件中使用的组件
const analyzeFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const usedComponents = [];
    
    // 检查基础组件的使用
    const baseComponentPatterns = [
      { pattern: /\bButton\b/g, component: 'Button', m3eEquivalent: 'm3e-button' },
      { pattern: /\bText\b/g, component: 'Text', m3eEquivalent: 'm3e-typography' },
      { pattern: /\bInput\b/g, component: 'Input', m3eEquivalent: 'm3e-text-field' },
      { pattern: /\bTextInput\b/g, component: 'TextInput', m3eEquivalent: 'm3e-text-field' },
      { pattern: /\bModal\b/g, component: 'Modal', m3eEquivalent: 'm3e-dialog' },
      { pattern: /\bCheckbox\b/g, component: 'Checkbox', m3eEquivalent: 'm3e-checkbox' },
      { pattern: /\bRadio\b/g, component: 'Radio', m3eEquivalent: 'm3e-radio-button' },
      { pattern: /\bSwitch\b/g, component: 'Switch', m3eEquivalent: 'm3e-switch' },
      { pattern: /\bSlider\b/g, component: 'Slider', m3eEquivalent: 'm3e-slider' },
      { pattern: /\bProgress\b/g, component: 'Progress', m3eEquivalent: 'm3e-progress-indicator' },
      { pattern: /\bSpinner\b/g, component: 'Spinner', m3eEquivalent: 'm3e-progress-indicator' },
      { pattern: /\bActivityIndicator\b/g, component: 'ActivityIndicator', m3eEquivalent: 'm3e-progress-indicator' },
      { pattern: /\bTabs\b/g, component: 'Tabs', m3eEquivalent: 'm3e-tabs' },
      { pattern: /\bTab\b/g, component: 'Tab', m3eEquivalent: 'm3e-tabs' },
      { pattern: /\bCard\b/g, component: 'Card', m3eEquivalent: 'm3e-card' },
      { pattern: /\bChip\b/g, component: 'Chip', m3eEquivalent: 'm3e-chips' },
      { pattern: /\bBadge\b/g, component: 'Badge', m3eEquivalent: 'm3e-badge' },
      { pattern: /\bAvatar\b/g, component: 'Avatar', m3eEquivalent: 'm3e-avatar' },
      { pattern: /\bDivider\b/g, component: 'Divider', m3eEquivalent: 'm3e-divider' },
      { pattern: /\bTooltip\b/g, component: 'Tooltip', m3eEquivalent: 'm3e-tooltip' },
      { pattern: /\bMenu\b/g, component: 'Menu', m3eEquivalent: 'm3e-menu' },
      { pattern: /\bDropdown\b/g, component: 'Dropdown', m3eEquivalent: 'm3e-menu' },
      { pattern: /\bSelect\b/g, component: 'Select', m3eEquivalent: 'm3e-menu' },
      { pattern: /\bList\b/g, component: 'List', m3eEquivalent: 'm3e-list' },
      { pattern: /\bListItem\b/g, component: 'ListItem', m3eEquivalent: 'm3e-list' },
      { pattern: /\bSearchBar\b/g, component: 'SearchBar', m3eEquivalent: 'm3e-search' },
      { pattern: /\bSearch\b/g, component: 'Search', m3eEquivalent: 'm3e-search' },
      { pattern: /\bDatePicker\b/g, component: 'DatePicker', m3eEquivalent: 'm3e-date-picker' },
      { pattern: /\bTimePicker\b/g, component: 'TimePicker', m3eEquivalent: 'm3e-time-picker' },
      { pattern: /\bBottomSheet\b/g, component: 'BottomSheet', m3eEquivalent: 'm3e-bottom-sheet' },
      { pattern: /\bSideSheet\b/g, component: 'SideSheet', m3eEquivalent: 'm3e-side-sheet' },
      { pattern: /\bSnackbar\b/g, component: 'Snackbar', m3eEquivalent: 'm3e-snackbar' },
      { pattern: /\bToast\b/g, component: 'Toast', m3eEquivalent: 'm3e-snackbar' },
      { pattern: /\bAppBar\b/g, component: 'AppBar', m3eEquivalent: 'm3e-app-bar' },
      { pattern: /\bNavigationBar\b/g, component: 'NavigationBar', m3eEquivalent: 'm3e-navigation-bar' },
      { pattern: /\bNavigationRail\b/g, component: 'NavigationRail', m3eEquivalent: 'm3e-navigation-rail' },
      { pattern: /\bFAB\b/g, component: 'FAB', m3eEquivalent: 'm3e-button' },
      { pattern: /\bFloatingActionButton\b/g, component: 'FloatingActionButton', m3eEquivalent: 'm3e-button' },
    ];
    
    for (const { pattern, component, m3eEquivalent } of baseComponentPatterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        // 检查是否已经在使用M3E组件
        const isAlreadyM3E = content.includes(`@/components/ui/${m3eEquivalent}`) || 
                            content.includes(`from '${m3eEquivalent}'`) ||
                            content.includes(`from "./${m3eEquivalent}"`);
        
        if (!isAlreadyM3E) {
          usedComponents.push({
            component,
            count: matches.length,
            m3eEquivalent,
            canMigrate: true
          });
        }
      }
    }
    
    return usedComponents.length > 0 ? { file: filePath, components: usedComponents } : null;
  } catch (error) {
    console.error(`分析文件 ${filePath} 时出错:`, error.message);
    return null;
  }
};

// 主函数
const main = () => {
  console.log('🔍 开始分析组件迁移机会...');
  console.log('='.repeat(50));
  
  // 获取可用的M3E组件
  const availableM3EComponents = getAvailableM3EComponents();
  console.log(`📦 可用的M3E组件 (${availableM3EComponents.length} 个):`);
  availableM3EComponents.forEach(comp => console.log(`   - ${comp}`));
  
  console.log('\n🔍 分析文件中的组件使用...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 扫描 ${allFiles.length} 个文件`);
  
  const migrationOpportunities = [];
  
  for (const file of allFiles) {
    const result = analyzeFile(file);
    if (result) {
      migrationOpportunities.push(result);
    }
  }
  
  console.log('\n📊 迁移机会分析结果:');
  console.log('='.repeat(50));
  
  if (migrationOpportunities.length === 0) {
    console.log('✅ 没有发现可以迁移到M3E组件的机会!');
    console.log('🎉 所有组件可能已经使用了M3E组件或不需要迁移。');
  } else {
    // 按组件类型统计
    const componentStats = {};
    
    migrationOpportunities.forEach(({ file, components }) => {
      components.forEach(({ component, count, m3eEquivalent }) => {
        if (!componentStats[component]) {
          componentStats[component] = {
            totalUsage: 0,
            files: [],
            m3eEquivalent
          };
        }
        componentStats[component].totalUsage += count;
        componentStats[component].files.push({ file, count });
      });
    });
    
    console.log(`🔄 发现 ${migrationOpportunities.length} 个文件可以迁移到M3E组件:`);
    
    // 按使用频率排序
    const sortedComponents = Object.entries(componentStats)
      .sort(([,a], [,b]) => b.totalUsage - a.totalUsage);
    
    sortedComponents.forEach(([component, { totalUsage, files, m3eEquivalent }]) => {
      console.log(`\n📦 ${component} → ${m3eEquivalent} (${totalUsage} 次使用, ${files.length} 个文件):`);
      files.slice(0, 5).forEach(({ file, count }) => {
        const relativePath = path.relative(process.cwd(), file);
        console.log(`   - ${relativePath} (${count} 次)`);
      });
      if (files.length > 5) {
        console.log(`   ... 还有 ${files.length - 5} 个文件`);
      }
    });
    
    console.log('\n🎯 建议的迁移优先级:');
    console.log('1. 高频使用的组件 (Button, Text, Input 等)');
    console.log('2. 核心功能组件 (Modal, Card, List 等)');
    console.log('3. 装饰性组件 (Badge, Chip, Avatar 等)');
  }
  
  console.log('\n📝 下一步行动:');
  console.log('1. 选择优先级最高的组件开始迁移');
  console.log('2. 创建迁移脚本自动替换组件导入');
  console.log('3. 测试迁移后的功能是否正常');
  console.log('4. 删除不再使用的旧组件');
  
  console.log('\n' + '='.repeat(50));
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findAllFiles, analyzeFile, getAvailableM3EComponents };
