import React from 'react';
import { View, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { Story } from '@/types/story';
import { StoryListItemHeader } from './story-list-item-header';
import { StoryListItemStats } from './story-list-item-stats';
import { StoryListItemTags } from './story-list-item-tags';

// Import gluestack-ui components


import { M3ECard } from '@/components/ui/m3e-card';

interface StoryListItemProps {
  story: Story;
  onOptionsPress?: () => void;
}

export default function StoryListItem({
  story,
  onOptionsPress,
}: StoryListItemProps) {
  const router = useRouter();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return 'Invalid Date';
    }
  };

  return (
    <M3ECard
      variant="filled"
      onPress={() => router.push(`/stories/${story.id}`)}
      className="mb-4"
    >
      <View className="flex-row items-center">
        <Image
          source={{ uri: story.coverImage }}
          alt={story.title}
          className="w-20 h-20 rounded-l-xl"
        />

        <View className="flex-1 p-4 justify-between min-h-20">
          <StoryListItemHeader
            title={story.title}
            onOptionsPress={onOptionsPress}
          />

          <StoryListItemStats
            views={story.views}
            likes={story.likes}
            updatedAt={story.updatedAt}
            formatDate={formatDate}
          />

          <StoryListItemTags
            themes={story.theme}
            isCompleted={story.isCompleted}
          />
        </View>
      </View>
    </M3ECard>
  );
}
