import '@/lib/i18n/config';
import { View } from 'react-native';
import '@/global.css';
import { NativeWindThemeProvider } from '@/lib/theme/nativewind-theme-provider';
import { useEffect } from 'react';
import { SplashScreen } from 'expo-router';
import {
  ensureSettingsStoreHydrated,
  useSettingsStore,
} from '@/lib/store/settings-store';
import { useAuthStore } from '@/lib/store/auth-store';

// Import custom hooks and components
import useFontLoader from '@/app/hooks/use-font-loader';
import useAuthRedirect from '@/app/hooks/use-auth-redirect';
import useSplashScreen from '@/app/hooks/use-splash-screen';
import ThemedStatusBar from '@/app/components/themed-status-bar';
import AppStack from '@/app/components/app-stack';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  // Ensure settings store starts its hydration process
  useEffect(() => {
    ensureSettingsStoreHydrated();
  }, []);

  // Load fonts
  const { fontsLoaded, fontError } = useFontLoader();

  // Wait for Zustand store hydration
  const settingsHasHydrated = useSettingsStore((state) => state._hasHydrated);
  const authHasHydrated = useAuthStore((state) => state._hasHydrated);
  const { initializeAuth } = useAuthStore();

  // Initialize auth state on mount if auth store has rehydrated
  useEffect(() => {
    if (authHasHydrated) {
      initializeAuth();
    }
  }, [authHasHydrated, initializeAuth]);

  // Handle authentication-based redirects
  const { authIsInitialized } = useAuthRedirect({
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
  });

  // Handle splash screen
  const { shouldShowSplash } = useSplashScreen({
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
    authIsInitialized,
  });

  // Get the theme mode from settings store - moved before conditional return to maintain hooks order
  const themeMode = useSettingsStore((state) => state.themeMode);
  const systemColorScheme = useSettingsStore(
    (state) => state.systemColorScheme
  );

  // Determine the actual mode to use
  const actualMode = themeMode === 'system' ? systemColorScheme : themeMode;

  // Keep splash screen visible while loading assets
  if (shouldShowSplash) {
    return null;
  }

  return (
    <NativeWindThemeProvider>
      <ThemedStatusBar />
      <AppStack />
    </NativeWindThemeProvider>
  );
}
