import React, { useState } from 'react';
import { Alert, Platform , View, Image } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { Pressable } from '@/components/base';

interface AvatarPickerProps {
  avatarUrl?: string | null;
  size?: number;
  onImageSelected: (uri: string) => void;
  loading?: boolean;
}

/**
 * AvatarPicker component
 *
 * A component that displays the current avatar and allows the user to select a new one
 * from the device's media library.
 */
export default function AvatarPicker({
  avatarUrl,
  size = 120,
  onImageSelected,
  loading = false,
}: AvatarPickerProps) {
  const { t } = useTranslation();
  const [requestingPermission, setRequestingPermission] = useState(false);

  // Request permission and open image picker
  const pickImage = async () => {
    try {
      setRequestingPermission(true);

      // Web platform handling
      if (Platform.OS === 'web') {
        // Create a file input element
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';

        // Create a promise to handle the file selection
        const fileSelectionPromise = new Promise<string>((resolve, reject) => {
          input.onchange = (event) => {
            const target = event.target as HTMLInputElement;
            const files = target.files;

            if (files && files.length > 0) {
              const file = files[0];

              // Check if the file is an image
              if (!file.type.startsWith('image/')) {
                reject(new Error('Selected file is not an image'));
                return;
              }

              // Create a FileReader to read the file as a data URL
              const reader = new FileReader();
              reader.onload = () => {
                const dataUrl = reader.result as string;
                resolve(dataUrl);
              };
              reader.onerror = () => {
                reject(new Error('Failed to read the selected file'));
              };
              reader.readAsDataURL(file);
            } else {
              reject(new Error('No file selected'));
            }
          };

          // Handle cancel
          input.oncancel = () => {
            reject(new Error('File selection cancelled'));
          };
        });

        // Trigger the file input click
        input.click();

        try {
          // Wait for the file selection
          const dataUrl = await fileSelectionPromise;
          onImageSelected(dataUrl);
        } catch (error) {
          console.log('File selection cancelled or failed:', error);
          // User cancelled or there was an error, no need to show an alert
        }

        setRequestingPermission(false);
        return;
      }

      // Native platform handling
      // Request media library permissions
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          t('permissions.mediaLibraryTitle', 'Permission Required'),
          t(
            'permissions.mediaLibraryMessage',
            'We need access to your media library to select an avatar image.'
          )
        );
        setRequestingPermission(false);
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      setRequestingPermission(false);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      setRequestingPermission(false);
      console.error('Error picking image:', error);
      Alert.alert(
        t('error', 'Error'),
        t(
          'profile.avatarPickerError',
          'There was an error selecting the image. Please try again.'
        )
      );
    }
  };

  // Determine the avatar source
  const avatarSource = avatarUrl
    ? { uri: avatarUrl }
    : require('@/assets/images/default-avatar.png');

  return (
    <View className="items-center justify-center my-4 relative">
      <View className="relative overflow-hidden border-3 border-primary-500 rounded-full"
        style={{ width: size, height: size }}
      >
        <Image
          source={avatarSource}
          className="w-full h-full"
          alt="User avatar"
        />

        {loading && (
          <View className="absolute inset-0 bg-black/50 items-center justify-center">
            <M3EProgressIndicator size="large" color="$primary500" />
          </View>
        )}
      </View>

      <Pressable className="absolute bottom-0 right-0 bg-primary-500 w-9 h-9 rounded-full items-center justify-center border-2 border-background-950"
        onPress={pickImage}
        disabled={loading || requestingPermission}
      >
        {requestingPermission ? (
          <M3EProgressIndicator size="small" color="$white" />
        ) : (
          <Camera size={18} color="white" />
        )}
      </Pressable>
    </View>
  );
}
