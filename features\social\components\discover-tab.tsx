import React, { useState } from 'react';
import { View } from 'react-native';
import { ScrollView } from '@/components/ui/scroll-view';

import { useColorScheme } from 'nativewind';
import { M3ESearch } from '@/components/ui/m3e-search';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { RecommendedAuthors } from './discover-tab/recommended-authors';
import { PopularTopics } from './discover-tab/popular-topics';

interface DiscoverTabProps {
  // Props for recommended users and topics if fetched
}

export function DiscoverTab({}: DiscoverTabProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    // 这里实现实时搜索逻辑
    if (text.trim().length > 0) {
      // 如果搜索内容不为空，可以导航到搜索结果页
      console.log('Search for:', text);
      // router.push(`/search?q=${encodeURIComponent(text)}`);
    }
  };

  const handleTopicPress = (topic: string) => {
    console.log('Topic pressed:', topic);
    // 导航到主题搜索结果
    router.push(`/search?topic=${encodeURIComponent(topic.substring(1))}`); // 移除#号
  };

  const handleUserPress = (userId: string) => {
    console.log('Navigate to user profile:', userId);
    // 跳转到用户个人资料页面
    router.push(`/users/${userId}`);
  };

  return (
    <ScrollView
      className={`flex-1 ${isDark ? 'bg-background-950' : 'bg-background-50'}`}
      contentContainerClassName="py-6 px-4 pb-16"
    >
      <View className="mb-6">
        <M3ESearch
          onSearch={handleSearch}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <RecommendedAuthors onUserPress={handleUserPress} />

      <PopularTopics onTopicPress={handleTopicPress} />
    </ScrollView>
  );
}
