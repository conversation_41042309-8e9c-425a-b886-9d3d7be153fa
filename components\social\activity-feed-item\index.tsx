import React from 'react';
import { View, Image } from 'react-native';
import { MessageSquare, Heart, GitBranch } from 'lucide-react-native';
import { User } from '@/types/user';

import { Text  } from '@/components/base';

import { M3ECard } from '@/components/ui/m3e-card';
import { TouchableOpacity } from 'react-native';

interface ActivityItem {
  id: string;
  type: 'comment' | 'like' | 'branch';
  user: User;
  storyTitle: string;
  content: string;
  time: string;
}

interface ActivityFeedItemProps {
  item: ActivityItem;
  onPress?: () => void;
}

export default function ActivityFeedItem({
  item,
  onPress,
}: ActivityFeedItemProps) {
  const renderIcon = () => {
    const color = getIconColor();
    const size = 16;

    switch (item.type) {
      case 'comment':
        return <MessageSquare size={size} color={color} />;
      case 'like':
        return <Heart size={size} color={color} fill={color} />;
      case 'branch':
        return <GitBranch size={size} color={color} />;
      default:
        return null;
    }
  };

  const getIconColor = () => {
    switch (item.type) {
      case 'comment':
        return '#6750A4'; // M3E primary
      case 'like':
        return '#BA1A1A'; // M3E error
      case 'branch':
        return '#006A6B'; // M3E tertiary
      default:
        return '#1D1B20'; // M3E on-surface
    }
  };

  return (
    <M3ECard variant="filled" onPress={onPress} className="mb-2">
      <View className="flex-row p-4">
        <Image
          source={{ uri: item.user.avatar }}
          className="w-12 h-12 rounded-full mr-4"
          alt={item.user.displayName}
        />

        <View className="flex-1">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-sm font-medium text-on-surface dark:text-on-surface-dark">
              {item.user.displayName}
            </Text>
            <Text className="text-xs text-on-surface-variant dark:text-on-surface-variant-dark">
              {item.time}
            </Text>
          </View>

          <View className="flex-row items-center mb-2 gap-2">
            {renderIcon()}
            <Text
              className="text-sm text-on-surface-variant dark:text-on-surface-variant-dark flex-shrink-1"
              numberOfLines={2}
            >
              {item.content}
            </Text>
          </View>

          <Text
            className="text-sm font-medium text-primary dark:text-primary-dark"
            numberOfLines={1}
          >
            《{item.storyTitle}》
          </Text>
        </View>
      </View>
    </M3ECard>
  );
}
