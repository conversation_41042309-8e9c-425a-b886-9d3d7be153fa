/**
 * M3E Input 兼容性组件
 *
 * 提供向后兼容的 Input、InputField、InputIcon、InputSlot 组件
 * 将 Gluestack UI Input API 映射到 M3E TextField
 */

import React, { forwardRef, useState, useContext, createContext } from 'react';
import { View, TextInputProps } from 'react-native';
import { M3ETextField, type M3ETextFieldProps } from './m3e-text-field';

// Gluestack Input 属性类型定义
export interface GluestackInputProps {
  variant?: 'outline' | 'underlined' | 'rounded';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  isDisabled?: boolean;
  isInvalid?: boolean;
  isReadOnly?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export interface GluestackInputFieldProps extends TextInputProps {
  placeholder?: string;
  className?: string;
}

export interface GluestackInputIconProps {
  as?: React.ComponentType<any>;
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  children?: React.ReactNode;
}

export interface GluestackInputSlotProps {
  className?: string;
  children?: React.ReactNode;
  onPress?: () => void;
}

// Input Context 用于在组件间传递状态
interface InputContextType {
  variant: string;
  size: string;
  isDisabled: boolean;
  isInvalid: boolean;
  leadingIcon?: React.ReactNode;
  trailingIcon?: React.ReactNode;
  trailingIconPress?: () => void;
  inputProps: Partial<GluestackInputFieldProps>;
}

const InputContext = createContext<InputContextType>({
  variant: 'outline',
  size: 'md',
  isDisabled: false,
  isInvalid: false,
  inputProps: {},
});

// 将 Gluestack 的 variant 映射到 M3E 的 variant
const mapGluestackVariantToM3E = (
  variant: string = 'outline'
): 'filled' | 'outlined' => {
  if (variant === 'underlined') return 'filled';
  return 'outlined';
};

// InputIcon 组件 - 收集图标信息
export const InputIcon = forwardRef<View, GluestackInputIconProps>(
  ({ as: AsComponent, className, size, children, ...props }, ref) => {
    // 这个组件不直接渲染，而是被 Input 组件收集
    return null;
  }
);

// InputSlot 组件 - 收集插槽信息
export const InputSlot = forwardRef<View, GluestackInputSlotProps>(
  ({ className, children, onPress, ...props }, ref) => {
    // 这个组件不直接渲染，而是被 Input 组件收集
    return null;
  }
);

// InputField 组件 - 收集输入框属性
export const InputField = forwardRef<any, GluestackInputFieldProps>(
  ({ className, ...props }, ref) => {
    // 这个组件不直接渲染，而是被 Input 组件收集
    return null;
  }
);

// 主要的 Input 组件 - 解析子组件并渲染 M3ETextField
export const Input = forwardRef<View, GluestackInputProps>(
  (
    {
      variant = 'outline',
      size = 'md',
      isDisabled = false,
      isInvalid = false,
      isReadOnly = false,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const [leadingIcon, setLeadingIcon] = useState<React.ReactNode>(null);
    const [trailingIcon, setTrailingIcon] = useState<React.ReactNode>(null);
    const [trailingIconPress, setTrailingIconPress] = useState<
      (() => void) | undefined
    >();
    const [inputProps, setInputProps] = useState<
      Partial<GluestackInputFieldProps>
    >({});

    // 解析子组件
    const parseChildren = (children: React.ReactNode) => {
      let leadingIconElement: React.ReactNode = null;
      let trailingIconElement: React.ReactNode = null;
      let trailingIconPressHandler: (() => void) | undefined;
      let fieldProps: Partial<GluestackInputFieldProps> = {};

      React.Children.forEach(children, (child) => {
        if (React.isValidElement(child)) {
          const childType = child.type as any;

          if (childType === InputIcon) {
            const iconProps = child.props as GluestackInputIconProps;
            const IconComponent = iconProps.as;
            const iconElement = IconComponent ? (
              <IconComponent {...iconProps} />
            ) : (
              iconProps.children
            );

            // 简单的启发式：第一个图标作为前置图标
            if (!leadingIconElement) {
              leadingIconElement = iconElement;
            } else {
              trailingIconElement = iconElement;
            }
          } else if (childType === InputSlot) {
            const slotProps = child.props as GluestackInputSlotProps;

            // 检查插槽内容是否包含图标
            const slotIcon = React.Children.toArray(slotProps.children).find(
              (slotChild) => {
                if (React.isValidElement(slotChild)) {
                  return (slotChild.type as any) === InputIcon;
                }
                return false;
              }
            );

            if (slotIcon && React.isValidElement(slotIcon)) {
              const iconProps = slotIcon.props as GluestackInputIconProps;
              const IconComponent = iconProps.as;
              const iconElement = IconComponent ? (
                <IconComponent {...iconProps} />
              ) : (
                iconProps.children
              );

              // 根据类名判断是前置还是后置
              const isTrailing =
                slotProps.className?.includes('pr-') ||
                slotProps.className?.includes('right');

              if (isTrailing) {
                trailingIconElement = iconElement;
                trailingIconPressHandler = slotProps.onPress;
              } else {
                leadingIconElement = iconElement;
              }
            } else {
              // 如果插槽不包含图标，将整个插槽内容作为图标
              const isTrailing =
                slotProps.className?.includes('pr-') ||
                slotProps.className?.includes('right');

              if (isTrailing) {
                trailingIconElement = slotProps.children;
                trailingIconPressHandler = slotProps.onPress;
              } else {
                leadingIconElement = slotProps.children;
              }
            }
          } else if (childType === InputField) {
            fieldProps = { ...fieldProps, ...child.props };
          }
        }
      });

      return {
        leadingIcon: leadingIconElement,
        trailingIcon: trailingIconElement,
        trailingIconPress: trailingIconPressHandler,
        inputProps: fieldProps,
      };
    };

    const parsed = parseChildren(children);

    // 构建 M3ETextField 属性
    const m3eProps: M3ETextFieldProps = {
      variant: mapGluestackVariantToM3E(variant),
      disabled: isDisabled || isReadOnly,
      error: isInvalid,
      leadingIcon: parsed.leadingIcon,
      trailingIcon: parsed.trailingIcon,
      onTrailingIconPress: parsed.trailingIconPress,
      className,
      ...parsed.inputProps,
    };

    return <M3ETextField ref={ref} {...m3eProps} />;
  }
);

// 设置 displayName 用于调试
Input.displayName = 'Input';
InputField.displayName = 'InputField';
InputIcon.displayName = 'InputIcon';
InputSlot.displayName = 'InputSlot';
