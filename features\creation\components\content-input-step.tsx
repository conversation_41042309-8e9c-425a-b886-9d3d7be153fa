import React from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { createStyles } from './content-input-step.styles';
import { Sparkles } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface ContentInputStepProps {
  content: string;
  onContentChange: (text: string) => void;
  onAiContinue?: () => void; // Optional AI continuation handler
  maxLength?: number;
}

const DEFAULT_MAX_LENGTH = 2000;

export function ContentInputStep({
  content,
  onContentChange,
  onAiContinue,
  maxLength = DEFAULT_MAX_LENGTH,
}: ContentInputStepProps) {
  const { mode } = useUnifiedTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('startYourStoryTitle')}</Text>
      <Text style={styles.description}>{t('startYourStoryDesc')}</Text>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder={t('storyContentPlaceholder')}
          placeholderTextColor={colors.placeholder}
          value={content}
          onChangeText={onContentChange}
          multiline="true"
          maxLength={maxLength}
        />
        <View style={styles.wordCount}>
          <Text style={styles.wordCountText}>
            {content.length} / {maxLength}
          </Text>
        </View>
      </View>

      {onAiContinue && ( // Only show button if handler is provided
        <TouchableOpacity
          style={styles.aiButton}
          onPress={onAiContinue}
        >
          <Sparkles color={colors.primary} size={20} />
          <Text style={styles.aiButtonText}>{t('aiContinueWriting')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
} 