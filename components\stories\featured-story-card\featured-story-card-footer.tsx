import React from 'react';
import { View } from 'react-native';
import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EText } from '@/components/ui/m3e-typography';

interface FeaturedStoryCardFooterProps {
  onPress?: () => void;
}

export function FeaturedStoryCardFooter({
  onPress,
}: FeaturedStoryCardFooterProps) {
  return (
    <M3EButtonFilled
      onPress={onPress}
      style={{
        borderRadius: 24,
        paddingHorizontal: 24,
        paddingVertical: 8,
      }}
    >
      <M3EText variant="label-large" style={{ color: 'white' }}>
        开始阅读
      </M3EText>
    </M3EButtonFilled>
  );
}

// 添加默认导出以兼容可能的默认导入
export default FeaturedStoryCardFooter;
