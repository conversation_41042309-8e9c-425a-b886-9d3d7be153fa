import React from 'react';
import { View } from 'react-native';
import { M3EText } from "@/components/ui/m3e-typography";

interface FeaturedStoryCardContentProps {
  summary: string;
  themeTags: string[];
}

export function FeaturedStoryCardContent({
  summary,
  themeTags,
}: FeaturedStoryCardContentProps) {
  return (
    <>
      <M3EText
        variant="bodyMedium"
        color="#FFFFFF"
        style={{ marginBottom: 12, lineHeight: 20 }}
      >
        {summary}
      </M3EText>

      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginBottom: 16,
          gap: 8,
        }}
      >
        {themeTags.map((tag) => (
          <View
            key={tag}
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 16,
            }}
          >
            <M3EText variant="labelSmall" color="#FFFFFF">
              {tag}
            </M3EText>
          </View>
        ))}
      </View>
    </>
  );
}
