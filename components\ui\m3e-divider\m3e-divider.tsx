import React from 'react';
import { View, Text } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

// Divider 的属性接口
export interface M3EDividerProps {
  /** 分割线方向 */
  orientation?: 'horizontal' | 'vertical';
  /** 分割线类型 */
  variant?: 'full-width' | 'inset' | 'middle-inset';
  /** 子标题文本（仅在 horizontal 且 variant='full-width' 时可用） */
  subheader?: string;
  /** 分割线厚度 */
  thickness?: number;
  /** 分割线颜色 */
  color?: string;
  /** 自定义样式类名 */
  className?: string;
}

// 获取分割线容器样式
const getDividerContainerClasses = (
  orientation: string,
  variant: string,
  hasSubheader: boolean
) => {
  if (orientation === 'vertical') {
    const paddingClasses = {
      'full-width': 'py-4',
      inset: 'pt-4',
      'middle-inset': 'py-4',
    };

    return `flex-col justify-stretch items-stretch ${
      paddingClasses[variant as keyof typeof paddingClasses] ||
      paddingClasses['full-width']
    }`;
  }

  // horizontal
  if (hasSubheader) {
    return 'flex-col justify-center gap-1 px-4';
  }

  const paddingClasses = {
    'full-width': 'flex-col justify-center',
    inset: 'flex-col justify-center pl-4',
    'middle-inset': 'flex-col justify-center px-4',
  };

  return (
    paddingClasses[variant as keyof typeof paddingClasses] ||
    paddingClasses['full-width']
  );
};

// 获取分割线样式
const getDividerLineClasses = (
  orientation: string,
  thickness: number,
  color: string
) => {
  const baseClasses =
    orientation === 'vertical' ? `w-px h-full` : `h-px w-full`;

  return `${baseClasses} ${color}`;
};

/**
 * M3E Divider 组件
 *
 * 基于 Material Design 3 规范的分割线组件，用于在列表和布局中分组内容。
 *
 * @example
 * ```tsx
 * // 水平分割线
 * <M3EDivider orientation="horizontal" variant="full-width" />
 *
 * // 带子标题的分割线
 * <M3EDivider
 *   orientation="horizontal"
 *   variant="full-width"
 *   subheader="Subheader"
 * />
 *
 * // 垂直分割线
 * <M3EDivider
 *   orientation="vertical"
 *   variant="middle-inset"
 *   className="h-32"
 * />
 * ```
 */
export const M3EDivider: React.FC<M3EDividerProps> = ({
  orientation = 'horizontal',
  variant = 'full-width',
  subheader,
  thickness = 1,
  color,
  className = '',
}) => {
  const { colors } = useUnifiedTheme();
  const hasSubheader =
    orientation === 'horizontal' && variant === 'full-width' && !!subheader;

  // 使用统一主题系统的颜色，如果没有自定义颜色
  const dividerColor = color || colors.outlineVariant;

  const containerClasses = getDividerContainerClasses(
    orientation,
    variant,
    hasSubheader
  );
  const lineClasses = getDividerLineClasses(
    orientation,
    thickness,
    'bg-transparent'
  );

  const combinedClasses = `${containerClasses} ${className}`;

  if (hasSubheader) {
    return (
      <View className={combinedClasses}>
        <View
          className={lineClasses}
          style={{ backgroundColor: dividerColor }}
        />
        <Text
          className="text-sm font-medium"
          style={{ color: colors.onSurfaceVariant }}
        >
          {subheader}
        </Text>
      </View>
    );
  }

  return (
    <View className={combinedClasses}>
      <View className={lineClasses} style={{ backgroundColor: dividerColor }} />
    </View>
  );
};

export default M3EDivider;
