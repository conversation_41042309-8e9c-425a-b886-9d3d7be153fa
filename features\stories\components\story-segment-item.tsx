import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';

// Import gluestack-ui components

import { Text } from '@/components/base';

import { M3EDivider } from '@/components/ui/m3e-divider';

interface StorySegmentItemProps {
  segment: StorySegment;
  isFirst: boolean;
  isLast: boolean;
  showDivider?: boolean; // Optional: to show divider between items
}

// Helper function from original component, can be moved to a util if used elsewhere
const formatDate = (isoString: string) => {
  const date = new Date(isoString);
  return (
    date.toLocaleDateString() +
    ' ' +
    date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  );
};

export default function StorySegmentItem({
  segment,
  isFirst,
  isLast,
  showDivider = true,
}: StorySegmentItemProps) {
  const { t } = useTranslation();

  return (
    <View
      className={`bg-surface-50 dark:bg-surface-900 p-4 rounded-md border border-outline-200 dark:border-outline-700 mb-4 overflow-hidden
        ${
          isFirst
            ? 'mt-2 border-l-3 border-l-primary-500 dark:border-l-primary-400'
            : ''
        }
        ${isLast ? 'mb-0' : ''}`}
    >
      {showDivider && !isFirst && (
        <M3EDivider className="my-2 w-[90%] self-center" />
      )}

      <Text className="text-base text-typography-900 dark:text-typography-100 mb-2 leading-6">
        {segment.content}
      </Text>

      {segment.is_ai_generated && (
        <Text className="text-xs italic text-accent-500 dark:text-accent-400 mt-1">
          {t('storyDetail.aiGenerated', 'AI Assisted')}
        </Text>
      )}

      <View className="flex flex-row justify-between items-center mt-2 pt-2 border-t border-outline-200 dark:border-outline-700">
        <Text className="text-sm font-medium text-primary-500 dark:text-primary-400">
          {segment.profiles?.username ||
            t('storyDetail.unknownAuthor', 'Unknown Author')}
        </Text>
        <Text className="text-xs text-secondary-500 dark:text-secondary-400">
          {formatDate(segment.created_at)}
        </Text>
      </View>
    </View>
  );
}
