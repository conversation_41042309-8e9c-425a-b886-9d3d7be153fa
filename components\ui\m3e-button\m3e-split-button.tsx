import React, { useState } from 'react';
import {
  View,
  Pressable,
  Text,
  Animated,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';

export interface M3ESplitButtonProps {
  /** Split Button样式 */
  variant?: 'filled' | 'filled-tonal' | 'outlined' | 'elevated';
  /** Split Button尺寸 */
  size?: 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';
  /** 主按钮标签 */
  label: string;
  /** 主按钮图标 */
  icon?: React.ReactNode;
  /** 下拉图标 */
  dropdownIcon?: React.ReactNode;
  /** 主按钮点击回调 */
  onPress?: () => void;
  /** 下拉按钮点击回调 */
  onDropdownPress?: () => void;
  /** 下拉按钮状态 */
  dropdownState?: 'enabled' | 'pressed' | 'selected' | 'hovered' | 'focused';
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
}

export const M3ESplitButton: React.FC<M3ESplitButtonProps> = ({
  variant = 'filled',
  size = 'medium',
  label,
  icon,
  dropdownIcon,
  onPress,
  onDropdownPress,
  dropdownState = 'enabled',
  disabled = false,
  style,
}) => {
  const theme = useAppTheme();
  const [mainPressed, setMainPressed] = useState(false);
  const [dropdownPressed, setDropdownPressed] = useState(false);
  const [mainPressAnim] = useState(new Animated.Value(1));
  const [dropdownPressAnim] = useState(new Animated.Value(1));

  const isDark = theme.dark;

  // 基于Figma设计的尺寸规范
  const getSizeSpecs = () => {
    switch (size) {
      case 'xsmall':
        return {
          height: 32,
          mainPaddingHorizontal: 12,
          dropdownWidth: 32,
          fontSize: 11,
          fontWeight: '500' as const,
          iconSize: 16,
          dropdownIconSize: 16,
          borderRadius: 16,
          letterSpacing: 0.5,
          lineHeight: 16,
          gap: 8,
        };
      case 'small':
        return {
          height: 40,
          mainPaddingHorizontal: 16,
          dropdownWidth: 40,
          fontSize: 14,
          fontWeight: '500' as const,
          iconSize: 18,
          dropdownIconSize: 18,
          borderRadius: 20,
          letterSpacing: 0.1,
          lineHeight: 20,
          gap: 8,
        };
      case 'medium':
        return {
          height: 48,
          mainPaddingHorizontal: 20,
          dropdownWidth: 48,
          fontSize: 16,
          fontWeight: '500' as const,
          iconSize: 20,
          dropdownIconSize: 20,
          borderRadius: 24,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 8,
        };
      case 'large':
        return {
          height: 56,
          mainPaddingHorizontal: 24,
          dropdownWidth: 56,
          fontSize: 16,
          fontWeight: '500' as const,
          iconSize: 24,
          dropdownIconSize: 24,
          borderRadius: 28,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 12,
        };
      case 'xlarge':
        return {
          height: 64,
          mainPaddingHorizontal: 28,
          dropdownWidth: 64,
          fontSize: 18,
          fontWeight: '500' as const,
          iconSize: 28,
          dropdownIconSize: 28,
          borderRadius: 32,
          letterSpacing: 0.1,
          lineHeight: 28,
          gap: 12,
        };
      default:
        return {
          height: 48,
          mainPaddingHorizontal: 20,
          dropdownWidth: 48,
          fontSize: 16,
          fontWeight: '500' as const,
          iconSize: 20,
          dropdownIconSize: 20,
          borderRadius: 24,
          letterSpacing: 0.1,
          lineHeight: 24,
          gap: 8,
        };
    }
  };

  const sizeSpecs = getSizeSpecs();

  // M3E颜色规范
  const getColors = () => {
    const baseColors = {
      primary: isDark ? '#D0BCFF' : '#6750A4',
      onPrimary: isDark ? '#381E72' : '#FFFFFF',
      primaryContainer: isDark ? '#4F378A' : '#EADDFF',
      onPrimaryContainer: isDark ? '#EADDFF' : '#4F378A',
      surface: isDark ? '#1D1B20' : '#FEF7FF',
      onSurface: isDark ? '#E6E0E9' : '#1D1B20',
      surfaceContainerLow: isDark ? '#1D1B20' : '#F7F2FA',
      outline: isDark ? '#938F99' : '#79747E',
    };

    switch (variant) {
      case 'filled':
        return {
          mainBackground: baseColors.primary,
          mainForeground: baseColors.onPrimary,
          dropdownBackground: baseColors.primary,
          dropdownForeground: baseColors.onPrimary,
          border: 'transparent',
          divider: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
      case 'filled-tonal':
        return {
          mainBackground: baseColors.primaryContainer,
          mainForeground: baseColors.onPrimaryContainer,
          dropdownBackground: baseColors.primaryContainer,
          dropdownForeground: baseColors.onPrimaryContainer,
          border: 'transparent',
          divider: isDark
            ? 'rgba(234, 221, 255, 0.12)'
            : 'rgba(79, 55, 138, 0.12)',
          stateLayer: isDark
            ? 'rgba(234, 221, 255, 0.08)'
            : 'rgba(79, 55, 138, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(234, 221, 255, 0.12)'
            : 'rgba(79, 55, 138, 0.12)',
        };
      case 'outlined':
        return {
          mainBackground: 'transparent',
          mainForeground: baseColors.primary,
          dropdownBackground: 'transparent',
          dropdownForeground: baseColors.primary,
          border: baseColors.outline,
          divider: baseColors.outline,
          stateLayer: isDark
            ? 'rgba(208, 188, 255, 0.08)'
            : 'rgba(103, 80, 164, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(208, 188, 255, 0.12)'
            : 'rgba(103, 80, 164, 0.12)',
        };
      case 'elevated':
        return {
          mainBackground: baseColors.surfaceContainerLow,
          mainForeground: baseColors.primary,
          dropdownBackground: baseColors.surfaceContainerLow,
          dropdownForeground: baseColors.primary,
          border: 'transparent',
          divider: isDark
            ? 'rgba(208, 188, 255, 0.12)'
            : 'rgba(103, 80, 164, 0.12)',
          stateLayer: isDark
            ? 'rgba(208, 188, 255, 0.08)'
            : 'rgba(103, 80, 164, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(208, 188, 255, 0.12)'
            : 'rgba(103, 80, 164, 0.12)',
        };
      default:
        return {
          mainBackground: baseColors.primary,
          mainForeground: baseColors.onPrimary,
          dropdownBackground: baseColors.primary,
          dropdownForeground: baseColors.onPrimary,
          border: 'transparent',
          divider: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
    }
  };

  const colors = getColors();

  // 禁用状态颜色
  const getDisabledColors = () => {
    return {
      background:
        variant === 'filled' ||
        variant === 'filled-tonal' ||
        variant === 'elevated'
          ? isDark
            ? 'rgba(230, 224, 233, 0.12)'
            : 'rgba(29, 27, 32, 0.12)'
          : 'transparent',
      foreground: isDark
        ? 'rgba(230, 224, 233, 0.38)'
        : 'rgba(29, 27, 32, 0.38)',
      border:
        variant === 'outlined'
          ? isDark
            ? 'rgba(230, 224, 233, 0.12)'
            : 'rgba(29, 27, 32, 0.12)'
          : 'transparent',
      divider: isDark ? 'rgba(230, 224, 233, 0.12)' : 'rgba(29, 27, 32, 0.12)',
    };
  };

  const disabledColors = getDisabledColors();

  // 获取下拉按钮状态颜色
  const getDropdownStateColors = () => {
    switch (dropdownState) {
      case 'selected':
        return {
          background:
            variant === 'filled'
              ? colors.dropdownBackground
              : colors.mainBackground,
          foreground:
            variant === 'filled'
              ? colors.dropdownForeground
              : colors.mainForeground,
          stateLayer: colors.pressedStateLayer,
        };
      case 'pressed':
        return {
          background: colors.dropdownBackground,
          foreground: colors.dropdownForeground,
          stateLayer: colors.pressedStateLayer,
        };
      case 'hovered':
      case 'focused':
        return {
          background: colors.dropdownBackground,
          foreground: colors.dropdownForeground,
          stateLayer: colors.stateLayer,
        };
      default:
        return {
          background: colors.dropdownBackground,
          foreground: colors.dropdownForeground,
          stateLayer: 'transparent',
        };
    }
  };

  const dropdownStateColors = getDropdownStateColors();

  const handleMainPressIn = () => {
    if (disabled) return;
    setMainPressed(true);
    Animated.spring(mainPressAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handleMainPressOut = () => {
    if (disabled) return;
    setMainPressed(false);
    Animated.spring(mainPressAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handleMainPress = () => {
    if (disabled) return;
    onPress?.();
  };

  const handleDropdownPressIn = () => {
    if (disabled) return;
    setDropdownPressed(true);
    Animated.spring(dropdownPressAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handleDropdownPressOut = () => {
    if (disabled) return;
    setDropdownPressed(false);
    Animated.spring(dropdownPressAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handleDropdownPress = () => {
    if (disabled) return;
    onDropdownPress?.();
  };

  const getContainerStyle = (): ViewStyle => {
    return {
      height: sizeSpecs.height,
      flexDirection: 'row',
      borderRadius: sizeSpecs.borderRadius,
      borderWidth: colors.border !== 'transparent' ? 1 : 0,
      borderColor: disabled ? disabledColors.border : colors.border,
      overflow: 'hidden',
      // 阴影（仅Elevated样式）
      ...(variant === 'elevated' && !disabled
        ? {
            boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.2)',
            elevation: 1,
          }
        : {}),
    };
  };

  const getMainButtonStyle = (): ViewStyle => {
    return {
      flex: 1,
      height: '100%',
      backgroundColor: disabled
        ? disabledColors.background
        : colors.mainBackground,
      paddingHorizontal: sizeSpecs.mainPaddingHorizontal,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
    };
  };

  const getDropdownButtonStyle = (): ViewStyle => {
    return {
      width: sizeSpecs.dropdownWidth,
      height: '100%',
      backgroundColor: disabled
        ? disabledColors.background
        : dropdownStateColors.background,
      alignItems: 'center',
      justifyContent: 'center',
      borderLeftWidth: 1,
      borderLeftColor: disabled ? disabledColors.divider : colors.divider,
      position: 'relative',
      overflow: 'hidden',
    };
  };

  const getTextStyle = (): TextStyle => {
    return {
      fontSize: sizeSpecs.fontSize,
      fontWeight: sizeSpecs.fontWeight,
      letterSpacing: sizeSpecs.letterSpacing,
      lineHeight: sizeSpecs.lineHeight,
      color: disabled ? disabledColors.foreground : colors.mainForeground,
      marginLeft: icon ? sizeSpecs.gap : 0,
    };
  };

  return (
    <View style={[getContainerStyle(), style]}>
      {/* 主按钮 */}
      <Animated.View
        style={[
          getMainButtonStyle(),
          { transform: [{ scale: mainPressAnim }] },
        ]}
      >
        <Pressable style={{
            width: '100%',
            height: '100%',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPressIn={handleMainPressIn}
          onPressOut={handleMainPressOut}
          onPress={handleMainPress}
          disabled={disabled}
        >
          {/* 主按钮状态层 */}
          {mainPressed && !disabled && (
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: colors.pressedStateLayer,
              }}
            />
          )}

          {/* 主按钮图标 */}
          {icon && (
            <View
              style={{ width: sizeSpecs.iconSize, height: sizeSpecs.iconSize }}
            >
              {icon}
            </View>
          )}

          {/* 主按钮标签 */}
          <Text style={getTextStyle()}>{label}</Text>
        </Pressable>
      </Animated.View>

      {/* 下拉按钮 */}
      <Animated.View
        style={[
          getDropdownButtonStyle(),
          { transform: [{ scale: dropdownPressAnim }] },
        ]}
      >
        <Pressable style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPressIn={handleDropdownPressIn}
          onPressOut={handleDropdownPressOut}
          onPress={handleDropdownPress}
          disabled={disabled}
        >
          {/* 下拉按钮状态层 */}
          {(dropdownPressed || dropdownState !== 'enabled') && !disabled && (
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: dropdownStateColors.stateLayer,
              }}
            />
          )}

          {/* 下拉图标 */}
          <View
            style={{
              width: sizeSpecs.dropdownIconSize,
              height: sizeSpecs.dropdownIconSize,
            }}
          >
            {dropdownIcon}
          </View>
        </Pressable>
      </Animated.View>
    </View>
  );
};

export default M3ESplitButton;
