import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EText } from '@/components/ui/m3e-typography';

interface ProfileActionsProps {
  onEditProfile?: () => void;
  onShareProfile?: () => void;
}

export function ProfileActions({
  onEditProfile,
  onShareProfile,
}: ProfileActionsProps) {
  const { t } = useTranslation();

  return (
    <View className="px-4 py-4 bg-background-950">
      <View className="flex flex-row justify-between space-x-3">
        {onEditProfile && (
          <M3EButtonFilled
            onPress={onEditProfile}
            style={{ flex: 1, marginRight: 8 }}
          >
            <M3EText variant="label-large" style={{ color: 'white' }}>
              {t('profile.editProfile', '编辑资料')}
            </M3EText>
          </M3EButtonFilled>
        )}
        {onShareProfile && (
          <M3EButtonFilled
            onPress={onShareProfile}
            style={{ flex: 1, marginLeft: 8 }}
          >
            <M3EText variant="label-large" style={{ color: 'white' }}>
              {t('profile.shareProfile', '分享')}
            </M3EText>
          </M3EButtonFilled>
        )}
      </View>
    </View>
  );
}
