/**
 * SearchBar 组件迁移脚本
 * 
 * 将使用旧 SearchBar 的文件迁移到 M3ESearch
 */

const fs = require('fs');
const path = require('path');

// 需要迁移的文件列表
const filesToMigrate = [
  'features/home/<USER>/home-screen.tsx',
  'features/search/screens/search-screen.tsx',
  'features/social/components/discover-tab.tsx'
];

// 迁移单个文件
const migrateFile = (filePath) => {
  try {
    console.log(`\n🔄 迁移文件: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 更新导入语句
    const oldImportRegex = /import\s*{\s*([^}]*SearchBar[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/search-bar['"];?/g;
    content = content.replace(oldImportRegex, (match, imports) => {
      console.log(`  ✅ 更新导入: SearchBar -> M3ESearch`);
      modified = true;
      // 替换 SearchBar 为 M3ESearch
      const newImports = imports.replace(/SearchBar/g, 'M3ESearch');
      return `import { ${newImports} } from '@/components/ui/m3e-search';`;
    });
    
    // 2. 更新组件使用
    const componentUsageRegex = /<SearchBar\b/g;
    content = content.replace(componentUsageRegex, (match) => {
      console.log(`  ✅ 更新组件使用: <SearchBar -> <M3ESearch`);
      modified = true;
      return '<M3ESearch';
    });
    
    // 3. 更新组件引用
    const componentRefRegex = /\bSearchBar\b(?!\w)/g;
    content = content.replace(componentRefRegex, (match) => {
      // 避免替换字符串中的内容
      if (content.indexOf(match) > -1) {
        const beforeChar = content[content.indexOf(match) - 1];
        const afterChar = content[content.indexOf(match) + match.length];
        
        // 如果是在 JSX 标签中或者是组件引用，则替换
        if (beforeChar === '<' || beforeChar === ' ' || beforeChar === '{' || afterChar === ' ' || afterChar === '>') {
          console.log(`  ✅ 更新组件引用: SearchBar -> M3ESearch`);
          modified = true;
          return 'M3ESearch';
        }
      }
      return match;
    });
    
    // 4. 保存文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ 文件迁移完成`);
      return true;
    } else {
      console.log(`  ⚠️  文件无需修改`);
      return false;
    }
    
  } catch (error) {
    console.error(`  ❌ 迁移文件失败: ${error.message}`);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移 SearchBar 组件到 M3ESearch');
  console.log('='.repeat(50));
  
  let successCount = 0;
  let totalCount = 0;
  
  for (const filePath of filesToMigrate) {
    totalCount++;
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log(`\n⚠️  文件不存在: ${filePath}`);
      continue;
    }
    
    const success = migrateFile(filePath);
    if (success) {
      successCount++;
    }
  }
  
  console.log('\n📊 迁移结果:');
  console.log('='.repeat(50));
  console.log(`总文件数: ${totalCount}`);
  console.log(`成功迁移: ${successCount}`);
  console.log(`跳过文件: ${totalCount - successCount}`);
  
  if (successCount > 0) {
    console.log('\n✅ 迁移完成！');
    console.log('下一步:');
    console.log('1. 检查迁移后的文件是否正常工作');
    console.log('2. 删除旧的 SearchBar 组件');
    console.log('3. 测试应用程序');
  } else {
    console.log('\n⚠️  没有文件需要迁移');
  }
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { migrateFile, filesToMigrate };
